{"id": "eea62e83-b910-4a32-894d-d9b4cb6481ae", "prevId": "00000000-0000-0000-0000-000000000000", "version": "7", "dialect": "postgresql", "tables": {"public.chat_analytics": {"name": "chat_analytics", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "session_id": {"name": "session_id", "type": "text", "primaryKey": false, "notNull": true}, "message_count": {"name": "message_count", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "user_message_count": {"name": "user_message_count", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "agent_message_count": {"name": "agent_message_count", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "duration": {"name": "duration", "type": "integer", "primaryKey": false, "notNull": false}, "lead_captured": {"name": "lead_captured", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "conversation_flow": {"name": "conversation_flow", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'[]'::jsonb"}, "intents": {"name": "intents", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'[]'::jsonb"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"chat_analytics_session_id_chat_sessions_id_fk": {"name": "chat_analytics_session_id_chat_sessions_id_fk", "tableFrom": "chat_analytics", "tableTo": "chat_sessions", "columnsFrom": ["session_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.chat_leads": {"name": "chat_leads", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "session_id": {"name": "session_id", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "company": {"name": "company", "type": "text", "primaryKey": false, "notNull": true}, "phone": {"name": "phone", "type": "text", "primaryKey": false, "notNull": false}, "business_type": {"name": "business_type", "type": "text", "primaryKey": false, "notNull": true}, "interests": {"name": "interests", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'[]'::jsonb"}, "qualification_stage": {"name": "qualification_stage", "type": "text", "primaryKey": false, "notNull": true}, "lead_score": {"name": "lead_score", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "is_converted": {"name": "is_converted", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"chat_leads_session_id_chat_sessions_id_fk": {"name": "chat_leads_session_id_chat_sessions_id_fk", "tableFrom": "chat_leads", "tableTo": "chat_sessions", "columnsFrom": ["session_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.chat_messages": {"name": "chat_messages", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "session_id": {"name": "session_id", "type": "text", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "sender": {"name": "sender", "type": "text", "primaryKey": false, "notNull": true}, "message_type": {"name": "message_type", "type": "text", "primaryKey": false, "notNull": true, "default": "'text'"}, "options": {"name": "options", "type": "jsonb", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}, "timestamp": {"name": "timestamp", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"chat_messages_session_id_chat_sessions_id_fk": {"name": "chat_messages_session_id_chat_sessions_id_fk", "tableFrom": "chat_messages", "tableTo": "chat_sessions", "columnsFrom": ["session_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.chat_sessions": {"name": "chat_sessions", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "started_at": {"name": "started_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "ended_at": {"name": "ended_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "qualification_stage": {"name": "qualification_stage", "type": "text", "primaryKey": false, "notNull": true, "default": "'initial'"}, "business_size": {"name": "business_size", "type": "text", "primaryKey": false, "notNull": false}, "current_topic": {"name": "current_topic", "type": "text", "primaryKey": false, "notNull": false}, "user_intent": {"name": "user_intent", "type": "text", "primaryKey": false, "notNull": false}, "pain_points": {"name": "pain_points", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'[]'::jsonb"}, "budget": {"name": "budget", "type": "text", "primaryKey": false, "notNull": false}, "timeline": {"name": "timeline", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.contacts": {"name": "contacts", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "company": {"name": "company", "type": "text", "primaryKey": false, "notNull": true}, "phone": {"name": "phone", "type": "text", "primaryKey": false, "notNull": true}, "business_type": {"name": "business_type", "type": "text", "primaryKey": false, "notNull": true}, "message": {"name": "message", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}