# Motivio Environment Configuration

# Application
NODE_ENV=development
PORT=5000
HOST=localhost

# AWS RDS PostgreSQL Database
DATABASE_URL=postgresql://motivio:<EMAIL>:5432/postgres

# Security
SESSION_SECRET=your-super-secret-session-key-change-this-in-production
CORS_ORIGIN=http://localhost:5000

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging
LOG_LEVEL=info

# Chat Configuration
CHAT_SESSION_TIMEOUT=1800000

# Production Settings (only for production)
# TRUST_PROXY=true
# SECURE_COOKIES=true
