version: '3.8'

services:
  # Motivio Application
  motivio:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "5000:5000"
    environment:
      - NODE_ENV=production
      - PORT=5000
      - HOST=0.0.0.0
      - DATABASE_URL=***************************************************/motivio
      - SESSION_SECRET=your-super-secret-session-key-change-this-in-production-please
      - CORS_ORIGIN=http://localhost:5000
      - RATE_LIMIT_WINDOW_MS=900000
      - RATE_LIMIT_MAX_REQUESTS=100
      - LOG_LEVEL=info
      - TRUST_PROXY=true
    depends_on:
      postgres:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - motivio-network
    healthcheck:
      test: ["CMD", "node", "-e", "require('http').get('http://localhost:5000/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # PostgreSQL Database
  postgres:
    image: postgres:16-alpine
    container_name: motivio-postgres
    environment:
      - POSTGRES_DB=motivio
      - POSTGRES_USER=motivio
      - POSTGRES_PASSWORD=motivio_password
      - POSTGRES_INITDB_ARGS=--encoding=UTF8 --locale=C
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/01-init.sql
      - ./docker/postgres/init-extensions.sql:/docker-entrypoint-initdb.d/02-extensions.sql
      - ./docker/postgres/postgresql.conf:/etc/postgresql/postgresql.conf
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U motivio -d motivio"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - motivio-network

  # pgAdmin for database management (development only)
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: motivio-pgadmin
    environment:
      - PGADMIN_DEFAULT_EMAIL=<EMAIL>
      - PGADMIN_DEFAULT_PASSWORD=admin123
      - PGADMIN_CONFIG_SERVER_MODE=False
    ports:
      - "8080:80"
    volumes:
      - pgadmin_data:/var/lib/pgadmin
      - ./docker/pgadmin/servers.json:/pgadmin4/servers.json
    depends_on:
      - postgres
    restart: unless-stopped
    networks:
      - motivio-network
    profiles:
      - dev

  # Redis (optional, for session storage in production)
  redis:
    image: redis:7-alpine
    container_name: motivio-redis
    ports:
      - "6379:6379"
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - motivio-network
    profiles:
      - redis

volumes:
  postgres_data:
    driver: local
  pgadmin_data:
    driver: local

networks:
  motivio-network:
    driver: bridge
